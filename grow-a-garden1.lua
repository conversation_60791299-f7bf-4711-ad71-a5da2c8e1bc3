local Fluent = loadstring(game:HttpGet("https://github.com/dawid-scripts/Fluent/releases/latest/download/main.lua"))()
local SaveManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/SaveManager.lua"))()
local InterfaceManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/InterfaceManager.lua"))()

-- Game Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Player and GUI references
local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")
local GameEvents = ReplicatedStorage:WaitForChild("GameEvents")

-- Seed Stock System
local SeedStock = {}
local selectedSeeds = {}
local autoBuyEnabled = false
local stockCheckConnection = nil

local Window = Fluent:CreateWindow({
    Title = "Fluent " .. Fluent.Version,
    SubTitle = "by XZery",
    TabWidth = 160,
    Size = UDim2.fromOffset(580, 460),
    Acrylic = true, -- The blur may be detectable, setting this to false disables blur entirely
    Theme = "Dark",
    MinimizeKey = Enum.KeyCode.LeftControl -- Used when theres no MinimizeKeybind
})

-- Seed Functions
local function GetSeedStock(IgnoreNoStock)
    local SeedShop = PlayerGui.Seed_Shop
    local Items = SeedShop:FindFirstChild("Blueberry", true).Parent

    local NewList = {}

    for _, Item in next, Items:GetChildren() do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end

        local StockText = MainFrame.Stock_Text.Text
        local StockCount = tonumber(StockText:match("%d+"))

        --// Seperate list
        if IgnoreNoStock then
            if StockCount <= 0 then continue end
            NewList[Item.Name] = StockCount
            continue
        end

        SeedStock[Item.Name] = StockCount
    end

    return IgnoreNoStock and NewList or SeedStock
end

local function BuySeed(Seed)
    GameEvents.BuySeedStock:FireServer(Seed)
end

local lastBuyTime = {}
local buyDelay = 1 -- 1 second delay between purchases of the same seed

local function checkAndBuySeeds()
    if not autoBuyEnabled then return end

    local currentTime = tick()
    local availableStock = GetSeedStock(true) -- Get only seeds with stock > 0

    for seedName, _ in pairs(selectedSeeds) do
        if availableStock[seedName] and availableStock[seedName] > 0 then
            -- Check if enough time has passed since last purchase
            if not lastBuyTime[seedName] or (currentTime - lastBuyTime[seedName]) >= buyDelay then
                BuySeed(seedName)
                lastBuyTime[seedName] = currentTime
                print("Auto-bought seed:", seedName, "Stock:", availableStock[seedName])

                -- Optional: Show notification for successful purchase
                Fluent:Notify({
                    Title = "Auto Buy",
                    Content = "Purchased: " .. seedName,
                    Duration = 2
                })
            end
        end
    end
end

--Fluent provides Lucide Icons https://lucide.dev/icons/ for the tabs, icons are optional
local Tabs = {
    Main = Window:AddTab({ Title = "Main", Icon = "" }),
    AutoBuy = Window:AddTab({ Title = "Auto Buy Seeds", Icon = "shopping-cart" }),
    Settings = Window:AddTab({ Title = "Settings", Icon = "settings" })
}

local Options = Fluent.Options
    
    local speed = Tabs.Main:AddSlider("Slider", {
        Title = "Speed",
        Description = "Set Your Speed",
        Default = 16,
        Min = 16,
        Max = 200,
        Rounding = 1,
    })

    speed:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.WalkSpeed = Value
    end)

    speed:SetValue(16)

    local jump = Tabs.Main:AddSlider("Slider", {
        Title = "jump",
        Description = "Set Your JumpPower",
        Default = 50,
        Min = 50,
        Max = 200,
        Rounding = 1,
    })

    jump:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.JumpPower = Value
    end)

    jump:SetValue(50)

    -- Fly toggle functionality
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Movement control
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local camera = workspace.CurrentCamera
            local userInputService = game:GetService("UserInputService")

            local velocity = Vector3.new(0, 0, 0)
            local flySpeed = 50

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- Simple directional movement based on camera
            if userInputService:IsKeyDown(Enum.KeyCode.W) then
                velocity = velocity + lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.S) then
                velocity = velocity - lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.A) then
                velocity = velocity - rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.D) then
                velocity = velocity + rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.Space) then
                velocity = velocity + upDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                velocity = velocity - upDirection * flySpeed
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
    end

    local flyToggle = Tabs.Main:AddToggle("FlyToggle", {
        Title = "Fly",
        Description = "Toggle fly mode",
        Default = false
    })

    flyToggle:OnChanged(function(Value)
        flyEnabled = Value
        if flyEnabled then
            enableFly()
        else
            disableFly()
        end
    end)

    -- Reset Character Button
    local resetButton = Tabs.Main:AddButton({
        Title = "Reset Character",
        Description = "Reset your character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end
    })

    -- Anti-AFK functionality
    local antiAfkEnabled = false
    local antiAfkConnection = nil
    local lastMoveTime = 0
    local isMoving = false
    local moveStartTime = 0

    local function startAntiAfk()
        antiAfkConnection = game:GetService("RunService").Heartbeat:Connect(function()
            if not antiAfkEnabled then return end

            local currentTime = tick()
            local player = game.Players.LocalPlayer
            local char = player.Character

            if char and char:FindFirstChild("Humanoid") and char:FindFirstChild("HumanoidRootPart") then
                local humanoid = char.Humanoid
                local rootPart = char.HumanoidRootPart

                -- Check if it's time to move (every 30 seconds)
                if not isMoving and (currentTime - lastMoveTime) >= 30 then
                    -- Start moving
                    isMoving = true
                    moveStartTime = currentTime
                    lastMoveTime = currentTime

                    -- Generate random direction
                    local randomX = math.random(-10, 10)
                    local randomZ = math.random(-10, 10)
                    local randomDirection = Vector3.new(randomX, 0, randomZ).Unit

                    -- Move in random direction
                    local targetPosition = rootPart.Position + (randomDirection * 5)
                    humanoid:MoveTo(targetPosition)
                end

                -- Stop moving after 2 seconds
                if isMoving and (currentTime - moveStartTime) >= 2 then
                    isMoving = false
                    humanoid:MoveTo(rootPart.Position) -- Stop at current position
                end
            end
        end)
    end

    local function stopAntiAfk()
        if antiAfkConnection then
            antiAfkConnection:Disconnect()
            antiAfkConnection = nil
        end
    end

    local antiAfkToggle = Tabs.Main:AddToggle("AntiAfkToggle", {
        Title = "Anti-AFK",
        Description = "Auto walk randomly every 30 seconds",
        Default = false
    })

    antiAfkToggle:OnChanged(function(Value)
        antiAfkEnabled = Value
        if antiAfkEnabled then
            startAntiAfk()
        else
            stopAntiAfk()
        end
    end)

-- Auto Buy Seeds Tab
do
    -- Get all available seeds from the game
    local function getAllSeeds()
        local allSeeds = {}
        pcall(function()
            local SeedShop = PlayerGui.Seed_Shop
            local Items = SeedShop:FindFirstChild("Blueberry", true).Parent

            for _, Item in next, Items:GetChildren() do
                local MainFrame = Item:FindFirstChild("Main_Frame")
                if MainFrame then
                    table.insert(allSeeds, Item.Name)
                end
            end
        end)
        return allSeeds
    end

    -- Multi-select dropdown for seed selection
    local seedMultiDropdown = Tabs.AutoBuy:AddDropdown("SeedSelection", {
        Title = "Select Seeds to Auto Buy",
        Description = "Choose which seeds to automatically purchase when in stock",
        Values = getAllSeeds(),
        Multi = true,
        Default = {}
    })

    seedMultiDropdown:OnChanged(function(Value)
        selectedSeeds = {}
        for _, seed in pairs(Value) do
            selectedSeeds[seed] = true
        end
        -- Save selected seeds
        saveSelectedSeeds()
    end)

    -- Auto Buy Toggle
    local autoBuyToggle = Tabs.AutoBuy:AddToggle("AutoBuyToggle", {
        Title = "Enable Auto Buy",
        Description = "Automatically buy selected seeds when they are in stock",
        Default = false
    })

    autoBuyToggle:OnChanged(function(Value)
        autoBuyEnabled = Value
        if autoBuyEnabled then
            -- Start stock checking
            stockCheckConnection = RunService.Heartbeat:Connect(function()
                checkAndBuySeeds()
            end)
            Fluent:Notify({
                Title = "Auto Buy",
                Content = "Auto buy enabled for selected seeds",
                Duration = 3
            })
        else
            -- Stop stock checking
            if stockCheckConnection then
                stockCheckConnection:Disconnect()
                stockCheckConnection = nil
            end
            Fluent:Notify({
                Title = "Auto Buy",
                Content = "Auto buy disabled",
                Duration = 3
            })
        end
    end)

    -- Manual stock check button
    local stockCheckButton = Tabs.AutoBuy:AddButton({
        Title = "Check Stock Now",
        Description = "Manually check and buy available seeds",
        Callback = function()
            checkAndBuySeeds()
            Fluent:Notify({
                Title = "Stock Check",
                Content = "Manual stock check completed",
                Duration = 2
            })
        end
    })

    -- Display current stock button
    local showStockButton = Tabs.AutoBuy:AddButton({
        Title = "Show Current Stock",
        Description = "Display current seed stock levels",
        Callback = function()
            local stock = GetSeedStock(false)
            local stockText = "Current Stock:\n"
            for seedName, count in pairs(stock) do
                stockText = stockText .. seedName .. ": " .. count .. "\n"
            end
            print(stockText)
            Fluent:Notify({
                Title = "Stock Info",
                Content = "Stock information printed to console",
                Duration = 3
            })
        end
    })
end

-- Addons:
-- SaveManager (Allows you to have a configuration system)
-- InterfaceManager (Allows you to have a interface managment system)

-- Hand the library over to our managers
SaveManager:SetLibrary(Fluent)
InterfaceManager:SetLibrary(Fluent)

-- Ignore keys that are used by ThemeManager.
-- (we dont want configs to save themes, do we?)
SaveManager:IgnoreThemeSettings()

-- You can add indexes of elements the save manager should ignore
SaveManager:SetIgnoreIndexes({})

-- Save selected seeds configuration
SaveManager:SetIgnoreIndexes({})
SaveManager:SetFolder("FluentScriptHub/grow-a-garden")

-- Load saved selected seeds
local function loadSelectedSeeds()
    local savedSeeds = SaveManager:Load("selectedSeeds")
    if savedSeeds then
        selectedSeeds = savedSeeds
        -- Update dropdown to show saved selections
        local seedNames = {}
        for seedName, _ in pairs(selectedSeeds) do
            table.insert(seedNames, seedName)
        end
        if #seedNames > 0 then
            Options.SeedSelection:SetValue(seedNames)
        end
    end
end

-- Save selected seeds
local function saveSelectedSeeds()
    SaveManager:Save("selectedSeeds", selectedSeeds)
end

-- use case for doing it this way:
-- a script hub could have themes in a global folder
-- and game configs in a separate folder per game
InterfaceManager:SetFolder("FluentScriptHub")
SaveManager:SetFolder("FluentScriptHub/specific-game")

InterfaceManager:BuildInterfaceSection(Tabs.Settings)
SaveManager:BuildConfigSection(Tabs.Settings)


Window:SelectTab(1)

Fluent:Notify({
    Title = "Fluent",
    Content = "The script has been loaded.",
    Duration = 8
})

-- You can use the SaveManager:LoadAutoloadConfig() to load a config
-- which has been marked to be one that auto loads!
SaveManager:LoadAutoloadConfig()

-- Load saved selected seeds after everything is set up
spawn(function()
    wait(1) -- Wait for GUI to fully load
    loadSelectedSeeds()
end)